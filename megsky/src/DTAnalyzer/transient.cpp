#include "transient.h"
#include <iomanip>
#include <sstream>
#include <stdexcept>
#include <iostream>
#include <sys/types.h>
#include"public_struct.h"



bool ComtradeWriter::write_cfg(const std::string& filename, const ComtradeConfig& config) {
    try {
        std::ofstream file(filename);
        if (!file.is_open()) {
            return false;
        }
        
        // 写入厂站名称和录波器编号
        file << config.station_name << "," << config.recorder_id << "\n";
        
        // 写入通道数量信息
        file << config.total_channels() << "," 
             << config.analog_count() << "A," 
             << config.digital_count() << "D\n";
        
        // 写入模拟量通道信息
        for (const auto& analog : config.analog_channels) {
            file << analog.index << ","
                 << analog.name << ","
                 << analog.phase << ","
                 << analog.element << ","
                 << analog.unit << ","
                 << std::fixed << std::setprecision(6) << analog.factor_a << ","
                 << std::fixed << std::setprecision(6) << analog.factor_b << ","
                 << analog.offset_time << ","
                 << analog.min_sample << ","
                 << analog.max_sample << "\n";
        }
        
        // 写入数字量通道信息
        for (const auto& digital : config.digital_channels) {
            file << digital.index << ","
                 << digital.name << ","
                 << digital.state << "\n";
        }
        
        // 写入频率信息
        file << std::fixed << std::setprecision(1) << config.frequency << "\n";
        
        // 写入采样率数量
        file << config.sampling_rates.size() << "\n";

        
        
        // 写入各采样率信息
        for (const auto& rate : config.sampling_rates) {
            file << std::fixed << std::setprecision(1) << rate.rate << ","
                 << rate.points << "\n";
        }
        
        // 写入开始时间和结束时间
        file << config.start_time << "\n";
        file << config.end_time << "\n";

        
        
        // 写入文件类型(固定为ASCII)
        file << "ASCII\n";
        
        return true;
    }
    catch (...) {
        return false;
    }
}

bool ComtradeWriter::write_sample_ascii(std::ofstream& file, const SamplePoint& sample) {
    if (!file.is_open()) {
        return false;
    }
    
    try {
        // 写入序号和时间
        file << sample.index << "," << sample.time;
        
        // 写入模拟量数据
        for (int value : sample.analog_values) {
            file << "," << value;
        }
        
        // 写入数字量数据
        for (int value : sample.digital_values) {
            file << "," << value;
        }
        
        file << "\n";
        return true;
    }
    catch (...) {
        return false;
    }
}

bool ComtradeWriter::write_dat_ascii(const std::string& filename, const ComtradeConfig& config, 
                                    const std::vector<SamplePoint>& samples) {
    try {
        std::ofstream file(filename);
        if (!file.is_open()) {
            return false;
        }
        
        // 验证采样点数据与配置是否匹配
        for (const auto& sample : samples) {
            if (sample.analog_values.size() != config.analog_count() ||
                sample.digital_values.size() != config.digital_count()) {
                return false; // 采样点数据与配置的通道数量不匹配
            }
        }
        
        // 写入所有采样点
        for (const auto& sample : samples) {
            if (!write_sample_ascii(file, sample)) {
                return false;
            }
        }
        
        return true;
    }
    catch (...) {
        return false;
    }
}

// int main() {
//     try {
//         // 创建配置信息
//         ComtradeConfig config;
//         config.station_name = "城东变电所";
//         config.recorder_id = "03";
//         config.frequency = 50.0;
//         config.start_time = "03/07/03,14:46:48.850000";
//         config.end_time = "03/07/03,14:46:49.010000";
        
//         // 添加模拟量通道
//         for (int i = 0; i < 8; ++i) {
//             AnalogChannel analog;
//             analog.index = i + 1;
//             analog.name = "ANALOG_" + std::to_string(i + 1);
//             analog.phase = (i % 3 == 0) ? "A" : (i % 3 == 1) ? "B" : "C";
//             analog.element = "LINE";
//             analog.unit = "A";
//             analog.factor_a = 1.0;
//             analog.factor_b = 0.0;
//             analog.offset_time = 0;
//             analog.min_sample = -32768;
//             analog.max_sample = 32767;
//             config.analog_channels.push_back(analog);
//         }
        
//         // 添加数字量通道
//         for (int i = 0; i < 4; ++i) {
//             DigitalChannel digital;
//             digital.index = i + 1;
//             digital.name = "DIGITAL_" + std::to_string(i + 1);
//             digital.state = 0;
//             config.digital_channels.push_back(digital);
//         }
        
//         // 添加采样率信息
//         SamplingRate rate1;
//         rate1.rate = 5000.0;
//         rate1.points = 6300;
//         config.sampling_rates.push_back(rate1);
        
//         SamplingRate rate2;
//         rate2.rate = 10.0;
//         rate2.points = 200;
//         config.sampling_rates.push_back(rate2);
        
//         // 写入CFG文件
//         bool cfg_result = ComtradeWriter::write_cfg("demo.cfg", config);
//         if (cfg_result) {
//             //cout << "CFG文件写入成功" << endl;
//         } else {
//             //cerr << "CFG文件写入失败" << endl;
//             return 1;
//         }
        
//         // 创建采样点数据
//         std::vector<SamplePoint> samples;
//         for (int i = 0; i < 10; ++i) {
//             SamplePoint sample;
//             sample.index = i + 1;
//             sample.time = i * 200;  // 时间偏移，单位微秒
            
//             // 生成模拟量示例数据
//             sample.analog_values.resize(config.analog_count());
//             for (size_t j = 0; j < config.analog_count(); ++j) {
//                 sample.analog_values[j] = 1000 * (i + 1) + j;
//             }
            
//             // 生成数字量示例数据
//             sample.digital_values.resize(config.digital_count());
//             for (size_t j = 0; j < config.digital_count(); ++j) {
//                 sample.digital_values[j] = (i % (j + 2)) == 0 ? 1 : 0;
//             }
            
//             samples.push_back(sample);
//         }
        
//         // 写入DAT文件
//         bool dat_result = ComtradeWriter::write_dat_ascii("demo.dat", config, samples);
//         if (dat_result) {
//             std::cout << "DAT文件写入成功" << std::endl;
//         } else {
//            // cerr << "DAT文件写入失败" << endl;
//             return 1;
//         }
        
//         return 0;
//     }
//     catch (const std::exception& e) {
//        // cerr << "发生错误: " << e.what() << endl;
//         return 1;
//     }
// }
    