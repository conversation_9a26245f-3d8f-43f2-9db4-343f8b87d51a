##############################################################################
# Copyright (c) 2021 山东梅格彤天电气有限公司 http://www.megsky.com
#
# @file    : Makefile
# @brief   : Data board library Makefile
# @note
#

##############################################################################

TZCDIR			 = ../..
APPNAME			 = DTAnalyzer
CROSS_COMPILE	?= aarch64-linux-gnu-
TARGET  		?= $(TZCDIR)/bin/$(APPNAME)



CC 				:= $(CROSS_COMPILE)g++

INCDIRS		:=  $(TZCDIR)/src/include \
				$(TZCDIR)/src/include/tzc_std \
				$(TZCDIR)/src/include/meg_pub \
				$(TZCDIR)/src/third_party/CJsonObject \

SRCDIRS		:=  $(TZCDIR)/src/$(APPNAME)  \
				$(TZCDIR)/src/third_party/CJsonObject \


INCLUDE		:= $(patsubst %, -I %, $(INCDIRS))

CPPFILES	:= $(foreach dir, $(SRCDIRS), $(wildcard $(dir)/*.cpp))
CPPFILENDIR	:= $(notdir  $(CPPFILES))
CPPOBJS		:= $(patsubst %, $(TZCDIR)/obj/$(APPNAME)/%, $(CPPFILENDIR:.cpp=.o))

CFILES		:= $(foreach dir, $(SRCDIRS), $(wildcard $(dir)/*.c))
CFILENDIR	:= $(notdir  $(CFILES))
COBJS		:= $(patsubst %, $(TZCDIR)/obj/$(APPNAME)/%, $(CFILENDIR:.c=.o))

OBJS		:= $(CPPOBJS) $(COBJS)

CFLAGS  	:= -Wall -Wl,-rpath=$(TZCDIR)/lib:$(TZCDIR)/lib/third_lib
LDFLAGS		:=-L$(TZCDIR)/lib -L$(TZCDIR)/lib/third_lib -L/usr/lib -lpthread -lpaho-mqtt3c -ltzcstd -lmegpub -lssl -lcrypto -lnsl 

VPATH		:= $(SRCDIRS)

.PHONY : clean packet


$(TARGET) : $(OBJS)
	$(CC) $(CFLAGS) $(OBJS) $(LDFLAGS) -o $@ 

$(CPPOBJS):$(TZCDIR)/obj/$(APPNAME)/%.o: %.cpp
	$(CC) -c $(CFLAGS) $(INCLUDE) $< -o $@ 

$(COBJS):$(TZCDIR)/obj/$(APPNAME)/%.o: %.c
	$(CC) -c $(CFLAGS) $(INCLUDE) $< -o $@ 


pac :
	objdump -d $(APPNAME)  > MM.s
	
clean :
	rm -rf $(OBJS)
	rm -rf $(TARGET)

packet :
	rm -f $(TZCDIR)/ova/$(APPNAME)/bin/$(APPNAME)
	rm -f $(TZCDIR)/ova/$(APPNAME).tar
	cp $(TARGET) $(TZCDIR)/ova/$(APPNAME)/bin/$(APPNAME)

	chmod 777 $(TZCDIR)/ova/$(APPNAME)/bin/startup_app.sh

		./appSignTool_x86 \
		-f $(TZCDIR)/bin \
		-b $(APPNAME) \
		-l $(TZCDIR)/ova/$(APPNAME)/lib \
		-e $(TZCDIR)/ova/$(APPNAME)/configFile \
		-e $(TZCDIR)/ova/$(APPNAME)/bin/ \
		-v 	CV01.001 \
		-o $(APPNAME) 

	chmod 777 DTAnalyzer.tar

		

